#!/usr/bin/env python3
"""
测试核心修复功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_duration_controller_comprehensive():
    """全面测试时长控制器修复"""
    print("=== 全面测试时长控制器修复 ===")
    
    try:
        from JianYingDraft.core.durationController import DurationController
        from JianYingDraft.core.configManager import AutoMixConfigManager
        
        # 创建控制器
        controller = DurationController()
        
        # 测试各种边界情况
        test_cases = [
            {
                'name': '正常素材',
                'materials': [
                    {'duration': 10000000, 'available_duration': 8000000},
                    {'duration': 15000000, 'available_duration': 12000000},
                    {'duration': 20000000, 'available_duration': 18000000}
                ]
            },
            {
                'name': '包含None值的素材',
                'materials': [
                    {'duration': None, 'available_duration': 8000000},
                    {'duration': 15000000, 'available_duration': None},
                    {'duration': None, 'available_duration': None},
                    {'duration': 20000000, 'available_duration': 18000000}
                ]
            },
            {
                'name': '全部为None的素材',
                'materials': [
                    {'duration': None, 'available_duration': None},
                    {'duration': None, 'available_duration': None},
                    {'duration': None, 'available_duration': None}
                ]
            },
            {
                'name': '混合数据类型',
                'materials': [
                    {'duration': 10000000, 'available_duration': 8000000},
                    {'duration': None, 'available_duration': 12000000},
                    {'duration': 15000000},  # 缺少 available_duration
                    {'available_duration': 18000000},  # 缺少 duration
                    {}  # 空字典
                ]
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
            materials = test_case['materials']
            
            try:
                # 测试基本时长计算
                durations = controller.calculate_segment_durations(materials, target_duration=35000000)
                print(f"✅ 基本时长计算成功: {[d/1000000 for d in durations]}秒")
                
                # 测试优化分配
                result = controller.optimize_duration_distribution(materials, target_duration=35000000)
                if result['success']:
                    print(f"✅ 优化分配成功: 总时长 {result['total_duration']/1000000:.1f}秒")
                else:
                    print(f"⚠️  优化分配返回失败: {result.get('error', 'Unknown')}")
                
                # 测试包含None优先级
                priorities_with_none = [1.0, None, 2.0] + [None] * (len(materials) - 3)
                if len(priorities_with_none) > len(materials):
                    priorities_with_none = priorities_with_none[:len(materials)]
                elif len(priorities_with_none) < len(materials):
                    priorities_with_none.extend([1.0] * (len(materials) - len(priorities_with_none)))
                
                durations_with_priority = controller.calculate_segment_durations(
                    materials, 
                    target_duration=35000000,
                    priorities=priorities_with_none
                )
                print(f"✅ None优先级测试成功: {[d/1000000 for d in durations_with_priority]}秒")
                
            except Exception as e:
                print(f"❌ 测试用例 {i} 失败: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
        
        # 测试极端情况
        print(f"\n--- 极端情况测试 ---")
        
        # 空列表
        try:
            empty_result = controller.calculate_segment_durations([], target_duration=35000000)
            print(f"✅ 空列表测试: {empty_result}")
        except Exception as e:
            print(f"❌ 空列表测试失败: {str(e)}")
            return False
        
        # 单个素材
        try:
            single_material = [{'duration': 10000000, 'available_duration': None}]
            single_result = controller.calculate_segment_durations(single_material, target_duration=35000000)
            print(f"✅ 单素材测试: {[d/1000000 for d in single_result]}秒")
        except Exception as e:
            print(f"❌ 单素材测试失败: {str(e)}")
            return False
        
        print(f"\n✅ 所有测试用例通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_original_error_scenario():
    """测试原始错误场景"""
    print("\n=== 测试原始错误场景 ===")
    
    try:
        from JianYingDraft.core.durationController import DurationController
        
        controller = DurationController()
        
        # 模拟可能导致原始错误的场景
        problematic_materials = [
            {'duration': None, 'available_duration': None, 'file_path': 'video1.mp4'},
            {'duration': 0, 'available_duration': None, 'file_path': 'video2.mp4'},
            {'duration': None, 'available_duration': 0, 'file_path': 'video3.mp4'},
            {'file_path': 'video4.mp4'},  # 完全没有时长信息
        ]
        
        print("测试可能导致 'NoneType * float' 错误的场景...")
        
        # 这个调用在修复前会导致错误
        result = controller.optimize_duration_distribution(problematic_materials, target_duration=35000000)
        
        if result['success']:
            print(f"✅ 原始错误场景修复成功!")
            print(f"   片段时长: {[d/1000000 for d in result['segment_durations']]}秒")
            print(f"   总时长: {result['total_duration']/1000000:.1f}秒")
        else:
            print(f"⚠️  返回失败但没有崩溃: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 原始错误场景测试失败: {str(e)}")
        # 如果这里失败，说明修复不完整
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_duration_controller_comprehensive()
    success2 = test_original_error_scenario()
    
    if success1 and success2:
        print("\n🎉 所有修复测试通过！")
        print("混剪功能中的 'NoneType * float' 错误已经修复！")
        sys.exit(0)
    else:
        print("\n❌ 修复测试失败，需要进一步检查")
        sys.exit(1)
