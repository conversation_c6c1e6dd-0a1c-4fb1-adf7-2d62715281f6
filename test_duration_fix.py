#!/usr/bin/env python3
"""
测试 DurationController 修复是否有效
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from JianYingDraft.core.durationController import DurationController
from JianYingDraft.core.configManager import AutoMixConfigManager

def test_duration_controller_with_none_values():
    """测试包含 None 值的情况"""
    print("=== 测试 DurationController 修复 ===")
    
    # 创建控制器实例
    controller = DurationController()
    
    # 测试用例1：包含 None 值的素材列表
    materials_with_none = [
        {'duration': 10000000, 'available_duration': 8000000},
        {'duration': None, 'available_duration': 12000000},  # duration 为 None
        {'duration': 15000000, 'available_duration': None},  # available_duration 为 None
        {'duration': None, 'available_duration': None},      # 两个都为 None
        {'duration': 20000000, 'available_duration': 18000000}
    ]
    
    print(f"测试素材数量: {len(materials_with_none)}")
    
    try:
        # 测试计算片段时长
        durations = controller.calculate_segment_durations(materials_with_none, target_duration=35000000)
        print(f"✅ 计算片段时长成功: {durations}")
        
        # 测试优化时长分配
        result = controller.optimize_duration_distribution(materials_with_none, target_duration=35000000)
        print(f"✅ 优化时长分配成功: {result['success']}")
        if result['success']:
            print(f"   调整后时长: {result['segment_durations']}")
            print(f"   最终总时长: {result['total_duration']/1000000:.1f}秒")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试用例2：空列表
    try:
        empty_durations = controller.calculate_segment_durations([], target_duration=30000000)
        print(f"✅ 空列表测试成功: {empty_durations}")
    except Exception as e:
        print(f"❌ 空列表测试失败: {str(e)}")
        return False
    
    # 测试用例3：包含 None 优先级
    try:
        priorities_with_none = [1.0, None, 2.0, None, 1.5]
        durations = controller.calculate_segment_durations(materials_with_none, 
                                                         target_duration=35000000,
                                                         priorities=priorities_with_none)
        print(f"✅ None 优先级测试成功: {durations}")
    except Exception as e:
        print(f"❌ None 优先级测试失败: {str(e)}")
        return False
    
    print("=== 所有测试通过 ===")
    return True

def test_config_manager():
    """测试配置管理器"""
    print("\n=== 测试配置管理器 ===")
    
    try:
        config = AutoMixConfigManager()
        duration_range = config.get_video_duration_range()
        print(f"✅ 配置获取成功: {duration_range}")
        
        min_dur, max_dur = duration_range
        print(f"   最小时长: {min_dur/1000000:.1f}秒")
        print(f"   最大时长: {max_dur/1000000:.1f}秒")
        
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success1 = test_config_manager()
    success2 = test_duration_controller_with_none_values()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！修复成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，需要进一步修复")
        sys.exit(1)
