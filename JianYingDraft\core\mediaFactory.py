"""
 * @file   : materialFactory.py
 * @time   : 16:10
 * @date   : 2024/3/24
 * @mail   : <EMAIL>
 * @creator: <PERSON><PERSON><PERSON>
 * @company: HiLand & RainyTop
"""
import os

from BasicLibrary.data.stringHelper import StringHelper
from BasicLibrary.environment.dynamicImporter import DynamicImporter
from pymediainfo import MediaInfo


class MediaFactory:
    """
    媒体工厂
    """

    @staticmethod
    def create(media_full_name: str, **kwargs):
        """
        根据素材来信创建素材实体
        :param media_full_name:
        :return:
        """
        if os.path.isfile(media_full_name) and not os.path.exists(media_full_name):
            return None

        try:
            # 解析媒体信息，增强对AI生成视频的兼容性
            parsed_data = MediaInfo.parse(media_full_name).to_data()
            tracks = parsed_data.get("tracks", [])

            if not tracks:
                print(f"⚠️  媒体文件没有轨道信息: {media_full_name}")
                return None

            # 智能查找合适的轨道
            media_info = None
            material_type = None

            # 首先检查文件扩展名，优先处理图片
            if media_full_name.endswith((".jpg", ".jpeg", ".png", ".bmp", ".gif", ".webp")):
                material_type = "Photo"
                # 对于图片，使用第一个轨道或创建默认信息
                if len(tracks) > 0:
                    media_info = tracks[0]
                else:
                    # 为图片创建默认媒体信息
                    media_info = {
                        'track_type': 'Video',  # 图片按视频处理
                        'duration': 5.0,  # 默认5秒
                        'width': 1920,
                        'height': 1080
                    }
            else:
                # 对于视频/音频文件，查找合适的轨道
                video_track = None
                audio_track = None

                for track in tracks:
                    track_type = track.get('track_type', '').lower()
                    if track_type == 'video' and video_track is None:
                        video_track = track
                    elif track_type == 'audio' and audio_track is None:
                        audio_track = track

                # 优先使用视频轨道，如果没有则使用音频轨道
                if video_track:
                    media_info = video_track
                    material_type = "Video"
                elif audio_track:
                    media_info = audio_track
                    material_type = "Audio"
                else:
                    # 如果没有找到标准轨道，使用第一个可用轨道
                    if len(tracks) > 1:
                        media_info = tracks[1]  # 保持原有逻辑
                    elif len(tracks) > 0:
                        media_info = tracks[0]
                    else:
                        print(f"⚠️  无法找到有效的媒体轨道: {media_full_name}")
                        return None

                    # 从轨道信息推断类型
                    track_type = media_info.get('track_type', '').lower()
                    if track_type:
                        material_type = StringHelper.upper_first_char(track_type)
                    else:
                        # 根据文件扩展名推断
                        ext = os.path.splitext(media_full_name)[1].lower()
                        if ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']:
                            material_type = "Video"
                        elif ext in ['.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a']:
                            material_type = "Audio"
                        else:
                            material_type = "Video"  # 默认为视频

            # 确保媒体信息包含必要字段，处理AI生成视频可能缺失的数据
            if media_info:
                # 确保有track_type字段
                if 'track_type' not in media_info or not media_info['track_type']:
                    media_info['track_type'] = material_type

                # 确保有duration字段，处理AI生成视频可能缺失时长的情况
                if 'duration' not in media_info or media_info['duration'] is None:
                    # 尝试从文件名或其他方式获取时长信息
                    duration_from_kwargs = kwargs.get('duration', 0)
                    if duration_from_kwargs > 0:
                        media_info['duration'] = duration_from_kwargs / 1000  # 转换为秒
                    else:
                        # 设置默认时长
                        if material_type == "Photo":
                            media_info['duration'] = 5.0  # 图片默认5秒
                        else:
                            media_info['duration'] = 10.0  # 视频/音频默认10秒
                            print(f"⚠️  媒体文件缺少时长信息，使用默认值: {media_full_name}")

                # 确保视频有宽高信息
                if material_type in ["Video", "Photo"]:
                    if 'width' not in media_info or media_info['width'] is None:
                        media_info['width'] = 1920  # 默认宽度
                    if 'height' not in media_info or media_info['height'] is None:
                        media_info['height'] = 1080  # 默认高度

            # 创建媒体对象
            package_name = f"JianYingDraft.core.media{material_type}"
            class_name = f"Media{material_type}"

            kwargs["mediaInfo"] = media_info
            kwargs["mediaFileFullName"] = media_full_name

            material = DynamicImporter.load_class(package_name, class_name, **kwargs)
            return material

        except Exception as e:
            print(f"❌ 创建媒体对象失败 {media_full_name}: {str(e)}")
            # 尝试创建一个基本的媒体对象
            try:
                # 根据文件扩展名创建默认媒体信息
                ext = os.path.splitext(media_full_name)[1].lower()
                if ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']:
                    material_type = "Photo"
                    default_info = {
                        'track_type': 'Video',
                        'duration': 5.0,
                        'width': 1920,
                        'height': 1080
                    }
                elif ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']:
                    material_type = "Video"
                    default_info = {
                        'track_type': 'Video',
                        'duration': kwargs.get('duration', 10000000) / 1000000,  # 转换为秒
                        'width': 1920,
                        'height': 1080
                    }
                elif ext in ['.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a']:
                    material_type = "Audio"
                    default_info = {
                        'track_type': 'Audio',
                        'duration': kwargs.get('duration', 10000000) / 1000000,  # 转换为秒
                    }
                else:
                    print(f"❌ 不支持的文件类型: {media_full_name}")
                    return None

                package_name = f"JianYingDraft.core.media{material_type}"
                class_name = f"Media{material_type}"

                kwargs["mediaInfo"] = default_info
                kwargs["mediaFileFullName"] = media_full_name

                print(f"⚠️  使用默认媒体信息创建对象: {media_full_name}")
                material = DynamicImporter.load_class(package_name, class_name, **kwargs)
                return material

            except Exception as fallback_error:
                print(f"❌ 创建默认媒体对象也失败 {media_full_name}: {str(fallback_error)}")
                return None
    

        
        
