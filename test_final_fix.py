#!/usr/bin/env python3
"""
最终测试所有NoneType运算错误修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_core_fixes():
    """测试核心修复功能"""
    print("=== 测试核心NoneType运算修复 ===")
    
    try:
        from JianYingDraft.core.durationController import DurationController
        from JianYingDraft.core.videoProcessor import VideoProcessor
        
        # 测试时长控制器
        print("1. 测试DurationController...")
        duration_controller = DurationController()
        
        # 包含各种None值的测试数据
        test_materials = [
            {
                'path': '/fake/video1.mp4',
                'duration': None,
                'available_duration': None,
                'width': None,
                'height': None
            },
            {
                'path': '/fake/video2.mp4',
                'duration': 15000000,
                'available_duration': None,
                'width': 1920,
                'height': None
            },
            {
                'path': '/fake/video3.mp4',
                'duration': None,
                'available_duration': 8000000,
                'width': None,
                'height': 1080
            }
        ]
        
        # 测试时长分配
        result = duration_controller.optimize_duration_distribution(test_materials, target_duration=35000000)
        
        if result['success']:
            print(f"✅ 时长分配成功: {[d/1000000 for d in result['segment_durations']]}秒")
        else:
            print(f"❌ 时长分配失败: {result.get('error', 'Unknown')}")
            return False
        
        # 测试视频处理器
        print("2. 测试VideoProcessor...")
        video_processor = VideoProcessor()
        
        # 测试包含None值的视频处理
        for i, material in enumerate(test_materials):
            try:
                segment_duration = result['segment_durations'][i]
                processed_result = video_processor.process_video_segment(material, segment_duration)
                print(f"✅ 视频{i+1}处理成功")
            except Exception as e:
                print(f"❌ 视频{i+1}处理失败: {str(e)}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    try:
        from JianYingDraft.core.durationController import DurationController
        
        controller = DurationController()
        
        # 测试用例1：全部为None的数据
        all_none_materials = [
            {'duration': None, 'available_duration': None},
            {'duration': None, 'available_duration': None},
            {'duration': None, 'available_duration': None}
        ]
        
        result1 = controller.optimize_duration_distribution(all_none_materials, target_duration=35000000)
        if result1['success']:
            print(f"✅ 全None数据测试成功: {[d/1000000 for d in result1['segment_durations']]}秒")
        else:
            print(f"⚠️  全None数据测试返回失败（预期行为）: {result1.get('error', 'Unknown')}")
        
        # 测试用例2：混合None和有效数据
        mixed_materials = [
            {'duration': 10000000, 'available_duration': None},
            {'duration': None, 'available_duration': 12000000},
            {'duration': None, 'available_duration': None}
        ]
        
        result2 = controller.optimize_duration_distribution(mixed_materials, target_duration=35000000)
        if result2['success']:
            print(f"✅ 混合数据测试成功: {[d/1000000 for d in result2['segment_durations']]}秒")
        else:
            print(f"❌ 混合数据测试失败: {result2.get('error', 'Unknown')}")
            return False
        
        # 测试用例3：包含None优先级
        priorities_with_none = [1.0, None, 2.0]
        durations = controller.calculate_segment_durations(
            mixed_materials, 
            target_duration=35000000,
            priorities=priorities_with_none
        )
        print(f"✅ None优先级测试成功: {[d/1000000 for d in durations]}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_video_compatibility():
    """测试AI视频兼容性"""
    print("\n=== 测试AI视频兼容性 ===")
    
    try:
        from JianYingDraft.core.materialScanner import MaterialScanner
        
        scanner = MaterialScanner()
        
        # 测试AI视频检测
        ai_files = [
            'wan2.2_generated_video.mp4',
            'ai_generated_content.mp4',
            'normal_video.mp4'
        ]
        
        for filename in ai_files:
            is_ai = scanner._detect_ai_generated(f"/fake/path/{filename}")
            expected = 'wan2.2' in filename or 'ai_generated' in filename
            if is_ai == expected:
                print(f"✅ AI检测正确: {filename} -> {'AI生成' if is_ai else '普通视频'}")
            else:
                print(f"❌ AI检测错误: {filename}")
                return False
        
        # 测试时长过滤（确保None值处理正确）
        test_videos = [
            {'duration': None, 'filename': 'video1.mp4'},
            {'duration': 15000000, 'filename': 'video2.mp4'},
            {'duration': 5000000, 'filename': 'video3.mp4'}
        ]
        
        scanner.videos = test_videos
        filtered = scanner.filter_videos_by_duration(min_duration=10000000)
        print(f"✅ 时长过滤测试成功: 过滤后{len(filtered)}个视频")
        
        return True
        
    except Exception as e:
        print(f"❌ AI视频兼容性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_original_error_reproduction():
    """重现并验证原始错误已修复"""
    print("\n=== 重现原始错误场景 ===")
    
    try:
        from JianYingDraft.core.durationController import DurationController
        from JianYingDraft.core.videoProcessor import VideoProcessor
        
        # 模拟导致原始错误的数据
        problematic_data = [
            {
                'path': '/fake/wan2.2_video.mp4',
                'duration': None,  # 这会导致 NoneType * float
                'available_duration': None,
                'width': None,     # 这会导致 NoneType / int
                'height': None,
                'is_ai_generated': True
            }
        ]
        
        # 测试时长计算（原来会出错的地方）
        controller = DurationController()
        result = controller.optimize_duration_distribution(problematic_data, target_duration=35000000)
        
        if not result['success']:
            print(f"⚠️  时长分配返回失败，但没有崩溃（这是好的）: {result.get('error', 'Unknown')}")
        else:
            print(f"✅ 原始错误场景修复成功: {result['segment_durations'][0]/1000000:.1f}秒")
        
        # 测试视频处理（原来会出错的地方）
        processor = VideoProcessor()
        try:
            processed = processor.process_video_segment(problematic_data[0], 10000000)
            print(f"✅ 视频处理原始错误场景修复成功")
        except Exception as e:
            print(f"❌ 视频处理仍有问题: {str(e)}")
            return False
        
        print("✅ 所有原始错误场景都已修复！")
        return True
        
    except Exception as e:
        print(f"❌ 原始错误重现测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 开始最终修复验证测试...")
    
    success1 = test_core_fixes()
    success2 = test_edge_cases()
    success3 = test_ai_video_compatibility()
    success4 = test_original_error_reproduction()
    
    if success1 and success2 and success3 and success4:
        print("\n🎉 所有修复验证测试通过！")
        print("✅ NoneType * float 错误已修复")
        print("✅ NoneType / int 错误已修复")
        print("✅ NoneType >= int 错误已修复")
        print("✅ AI生成视频兼容性已增强")
        print("✅ 混剪功能已恢复正常")
        print("\n现在可以安全地运行混剪功能了！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
