#!/usr/bin/env python3
"""
测试AI生成视频处理修复
"""

import sys
import os
import tempfile
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_media_factory_with_ai_videos():
    """测试MediaFactory对AI生成视频的处理"""
    print("=== 测试MediaFactory AI视频处理 ===")
    
    try:
        from JianYingDraft.core.mediaFactory import MediaFactory
        
        # 模拟AI生成视频的各种情况
        test_cases = [
            {
                'name': '正常视频文件',
                'filename': 'normal_video.mp4',
                'should_work': True
            },
            {
                'name': 'wan2.2生成的视频',
                'filename': 'wan2.2_generated_video.mp4',
                'should_work': True
            },
            {
                'name': 'AI生成视频',
                'filename': 'ai_generated_content.mp4',
                'should_work': True
            },
            {
                'name': '图片文件',
                'filename': 'test_image.jpg',
                'should_work': True
            }
        ]
        
        for test_case in test_cases:
            print(f"\n--- 测试: {test_case['name']} ---")
            
            # 创建临时文件进行测试
            with tempfile.NamedTemporaryFile(suffix=os.path.splitext(test_case['filename'])[1], delete=False) as temp_file:
                temp_path = temp_file.name
                # 写入一些基本内容
                temp_file.write(b"fake media content for testing")
            
            try:
                # 测试MediaFactory.create方法
                media = MediaFactory.create(temp_path, duration=15000000)  # 15秒
                
                if media is not None:
                    print(f"✅ 成功创建媒体对象: {type(media).__name__}")
                    print(f"   文件路径: {media.file_Path}")
                    print(f"   时长: {media.duration/1000000:.1f}秒")
                    if hasattr(media, 'width') and hasattr(media, 'height'):
                        print(f"   分辨率: {media.width}x{media.height}")
                else:
                    if test_case['should_work']:
                        print(f"❌ 创建媒体对象失败，但应该成功")
                    else:
                        print(f"✅ 正确地返回了None")
                        
            except Exception as e:
                print(f"❌ 创建媒体对象时出现异常: {str(e)}")
                if test_case['should_work']:
                    print("   这是一个需要修复的问题")
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_path)
                except:
                    pass
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        print("这可能是由于缺少依赖库导致的")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_material_scanner_with_ai_videos():
    """测试MaterialScanner对AI生成视频的处理"""
    print("\n=== 测试MaterialScanner AI视频处理 ===")
    
    try:
        from JianYingDraft.core.materialScanner import MaterialScanner
        
        scanner = MaterialScanner()
        
        # 测试AI视频检测功能
        test_files = [
            'normal_video.mp4',
            'wan2.2_generated_video.mp4',
            'ai_generated_content.mp4',
            'artificial_video.mp4',
            'regular_content.mp4'
        ]
        
        print("测试AI视频检测:")
        for filename in test_files:
            is_ai = scanner._detect_ai_generated(f"/fake/path/{filename}")
            print(f"  {filename}: {'🤖 AI生成' if is_ai else '📹 普通视频'}")
        
        # 测试备用视频信息创建
        print("\n测试备用视频信息创建:")
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            temp_path = temp_file.name
            temp_file.write(b"fake video content")
        
        try:
            scanner._create_fallback_video_info(temp_path)
            if scanner.videos:
                video_info = scanner.videos[-1]  # 获取最后添加的视频
                print(f"✅ 成功创建备用视频信息:")
                print(f"   文件名: {video_info['filename']}")
                print(f"   时长: {video_info['duration']/1000000:.1f}秒")
                print(f"   分辨率: {video_info['width']}x{video_info['height']}")
                print(f"   AI生成: {video_info.get('is_ai_generated', False)}")
            else:
                print("❌ 未能创建备用视频信息")
                
        finally:
            try:
                os.unlink(temp_path)
            except:
                pass
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_duration_controller_with_ai_videos():
    """测试DurationController对AI视频数据的处理"""
    print("\n=== 测试DurationController AI视频数据处理 ===")
    
    try:
        from JianYingDraft.core.durationController import DurationController
        
        controller = DurationController()
        
        # 模拟AI生成视频可能产生的数据
        ai_video_materials = [
            {
                'path': '/fake/wan2.2_video1.mp4',
                'filename': 'wan2.2_video1.mp4',
                'duration': None,  # AI视频可能缺少时长
                'available_duration': None,
                'width': 1920,
                'height': 1080,
                'is_ai_generated': True,
                'fallback_used': True
            },
            {
                'path': '/fake/ai_generated_video2.mp4',
                'filename': 'ai_generated_video2.mp4',
                'duration': 12000000,  # 12秒
                'available_duration': None,  # 缺少可用时长
                'width': None,  # 缺少宽度
                'height': None,  # 缺少高度
                'is_ai_generated': True
            },
            {
                'path': '/fake/normal_video.mp4',
                'filename': 'normal_video.mp4',
                'duration': 15000000,  # 15秒
                'available_duration': 12000000,  # 12秒
                'width': 1920,
                'height': 1080,
                'is_ai_generated': False
            }
        ]
        
        print(f"测试 {len(ai_video_materials)} 个AI视频素材的时长计算...")
        
        # 测试时长计算
        result = controller.optimize_duration_distribution(ai_video_materials, target_duration=35000000)
        
        if result['success']:
            print(f"✅ AI视频时长分配成功:")
            print(f"   片段时长: {[d/1000000 for d in result['segment_durations']]}秒")
            print(f"   总时长: {result['total_duration']/1000000:.1f}秒")
            print(f"   验证消息: {result['message']}")
            
            # 检查是否正确处理了None值
            for i, duration in enumerate(result['segment_durations']):
                if duration is None:
                    print(f"❌ 第{i+1}个片段时长仍为None，修复不完整")
                    return False
            
            print("✅ 所有片段时长都是有效数值")
            
        else:
            print(f"❌ AI视频时长分配失败: {result.get('error', 'Unknown error')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🤖 开始测试AI生成视频处理修复...")
    
    success1 = test_media_factory_with_ai_videos()
    success2 = test_material_scanner_with_ai_videos()
    success3 = test_duration_controller_with_ai_videos()
    
    if success1 and success2 and success3:
        print("\n🎉 所有AI视频处理测试通过！")
        print("现在可以正确处理wan2.2等AI生成的视频了。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
